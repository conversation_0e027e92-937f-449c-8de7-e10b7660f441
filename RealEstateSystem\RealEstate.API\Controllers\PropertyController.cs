﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using System.Text.Json;
using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.API.Controllers
{
    /// <summary>
    /// Controller for managing property listings and related operations
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class PropertyController : BaseController
    {
        private readonly IPropertyService _propertyService;
        private readonly IMediaServices _mediaServices;
        private readonly IFileStorageService _fileStorageService;
        private readonly ILogger<PropertyController> _logger;
        private readonly List<PropertyStatus> _statusApproved = new List<PropertyStatus> { PropertyStatus.Approved };

        public PropertyController(IPropertyService propertyService, ILogger<PropertyController> logger, IMediaServices mediaServices, IFileStorageService fileStorageService)
        {
            _propertyService = propertyService;
            _logger = logger;
            _mediaServices = mediaServices;
            _fileStorageService = fileStorageService;
        }

        /// <summary>
        /// Get a property by its ID
        /// </summary>
        /// <param name="propertyId">The unique identifier of the property</param>
        /// <returns>The property details</returns>
        /// <response code="200">Returns the property</response>
        /// <response code="404">If the property is not found</response>
        [AllowAnonymous]
        [HttpGet("{propertyId}")]
        public async Task<IActionResult> GetPropertyById(Guid propertyId)
        {
            var result = await _propertyService.GetPropertyByIdAsync(propertyId);
            return HandleResult(result);
        }

        /// <summary>
        /// Create a new property
        /// </summary>
        /// <param name="propertyDto">The property data</param>
        /// <returns>The created property</returns>
        /// <response code="200">Returns the newly created property</response>
        /// <response code="400">If the property data is invalid</response>
        /// <response code="401">If the user is not authenticated</response>
        [HttpPost]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> CreateProperty([FromBody] CreatePropertyDto propertyDto)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("Invalid user");

            _logger.LogInformation("Create: {JsonData}", JsonSerializer.Serialize(new
            {
                userId,
                propertyDto
            }));

            var result = await _propertyService.CreatePropertyAsync(propertyDto, userId.Value);

            if (result.IsSuccess)
            {
                var mediaIds = propertyDto.UploadedFiles?.Select(x => x.Id).ToList();
                if (mediaIds != null && mediaIds.Any())
                {
                    await MoveTempImagesToPropertyFolder(result.Value.Id, mediaIds, propertyDto.UploadedFiles);
                }
            }

            return HandleResult(result);
        }

        /// <summary>
        /// Update an existing property
        /// </summary>
        /// <param name="propertyId">The ID of the property to update</param>
        /// <param name="propertyDto">The updated property data</param>
        /// <returns>The updated property</returns>
        /// <response code="201">Returns the updated property</response>
        /// <response code="400">If the property data is invalid</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="403">If the user is not authorized to update this property</response>
        /// <response code="404">If the property is not found</response>
        [HttpPut("{propertyId}")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> UpdateProperty(Guid propertyId, [FromBody] CreatePropertyDto propertyDto)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("Invalid user");

            _logger.LogInformation("Update: {JsonData}", JsonSerializer.Serialize(new
            {
                userId,
                propertyDto
            }));

            var result = await _propertyService.UpdatePropertyAsync(propertyId, propertyDto, userId.Value);

            if (result.IsSuccess)
            {
                if (propertyDto.UploadedFiles != null && propertyDto.UploadedFiles.Any())
                {
                    await UpdateExistingMediaFiles(propertyId, propertyDto.UploadedFiles);
                }
            }

            return HandleResult(result);
        }

        /// <summary>
        /// Delete a property
        /// </summary>
        /// <param name="propertyId">The ID of the property to delete</param>
        /// <returns>No content if successful</returns>
        /// <response code="204">If the property was successfully deleted</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="403">If the user is not authorized to delete this property</response>
        /// <response code="404">If the property is not found</response>
        [HttpDelete("{propertyId}")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> DeleteProperty(Guid propertyId)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("Invalid user");

            var result = await _propertyService.DeletePropertyAsync(propertyId, userId.Value);
            return HandleResult(result);
        }

        /// <summary>
        /// Delete multiple properties in bulk
        /// </summary>
        /// <param name="request">The list of property IDs to delete</param>
        /// <returns>No content if successful</returns>
        /// <response code="204">If the properties were successfully deleted</response>
        /// <response code="400">If the request is invalid or some properties couldn't be deleted</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="403">If the user is not authorized to delete one or more properties</response>
        [HttpDelete("bulk")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> DeleteProperties([FromBody] BulkPropertyIdsDto request)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("Invalid user");

            var result = await _propertyService.DeletePropertiesAsync(request.PropertyIds, userId.Value);
            return HandleResult(result);
        }

        /// <summary>
        /// Update the status of a property
        /// </summary>
        /// <param name="propertyId">The ID of the property to update</param>
        /// <param name="request">The status update information</param>
        /// <returns>No content if successful</returns>
        /// <response code="204">If the status was successfully updated</response>
        /// <response code="400">If the request is invalid</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="403">If the user is not authorized to update this property</response>
        /// <response code="404">If the property is not found</response>
        [HttpPut("{propertyId}/status")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> UpdateStatus(Guid propertyId, [FromBody] UpdateStatusDto request)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("Invalid user");

            var result = await _propertyService.UpdateStatusAsync(propertyId, userId.Value, request);
            return HandleResult(result);
        }

        /// <summary>
        /// Update the status of multiple properties in bulk
        /// </summary>
        /// <param name="request">The bulk status update information containing property IDs, status, and comment</param>
        /// <returns>No content if successful</returns>
        /// <response code="204">If the statuses were successfully updated</response>
        /// <response code="400">If the request is invalid or some properties couldn't be updated</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="403">If the user is not authorized to update one or more properties</response>
        [HttpPut("bulk/status")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> UpdateStatusBulk([FromBody] BulkUpdateStatusDto request)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("Invalid user");

            var updateStatusDto = new UpdateStatusDto { Status = request.Status, Comment = request.Comment };
            var result = await _propertyService.UpdateStatusBulkAsync(request.PropertyIds, userId.Value, updateStatusDto);
            return HandleResult(result);
        }

        /// <summary>
        /// Update the highlight status of a property
        /// </summary>
        /// <param name="propertyId">The ID of the property to update</param>
        /// <param name="request">The highlight update information</param>
        /// <returns>The updated property if successful</returns>
        /// <response code="200">If the highlight status was successfully updated</response>
        /// <response code="400">If the request is invalid or the user has insufficient funds</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="403">If the user is not authorized to update this property</response>
        /// <response code="404">If the property is not found</response>
        [HttpPut("{propertyId}/highlight")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> UpdateHighlight(Guid propertyId, [FromBody] UpdateHighlightDto request)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("Invalid user");

            var result = await _propertyService.UpdateHighlightWithPaymentAsync(propertyId, userId.Value, request.IsHighlighted);
            return HandleResult(result);
        }

        /// <summary>
        /// Update the highlight status of multiple properties in bulk with payment processing
        /// </summary>
        /// <param name="request">The bulk highlight update information containing property IDs and highlight status</param>
        /// <returns>Detailed result of the bulk operation</returns>
        /// <response code="200">Returns the detailed result of the bulk operation</response>
        /// <response code="400">If the request is invalid or wallet balance is insufficient</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="403">If the user is not authorized to update one or more properties</response>
        [HttpPut("bulk/highlight")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> UpdateHighlightBulk([FromBody] BulkUpdateHighlightDto request)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("Invalid user");

            var result = await _propertyService.UpdateHighlightBulkWithPaymentAsync(request.PropertyIds, userId.Value, request.IsHighlighted);
            return HandleResult(result);
        }

        /// <summary>
        /// Get all properties owned by the current user
        /// </summary>
        /// <param name="status">Optional filter by property status</param>
        /// <param name="page">Page number for pagination</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <returns>Paged list of properties owned by the current user</returns>
        /// <response code="200">Returns the list of properties</response>
        /// <response code="400">If the user is invalid</response>
        /// <response code="401">If the user is not authenticated</response>
        [HttpGet("me")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> GetAllPropertiesByUser(
            [FromQuery] List<PropertyStatus> status,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("Invalid user");

            var result = await _propertyService.GetPropertyByUserWithStatusAsync(userId.Value, status, page, pageSize);
            return HandleResult(result);
        }

        /// <summary>
        /// Upload images for a property
        /// </summary>
        /// <param name="propertyId">Optional ID of the property to associate with the images</param>
        /// <param name="files">The image files to upload</param>
        /// <returns>List of uploaded property media</returns>
        /// <response code="200">Returns the list of uploaded media</response>
        /// <response code="400">If the request is invalid or no files were uploaded</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="404">If the property ID is provided but not found</response>
        [HttpPost("upload-images")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> UploadPropertyImages([FromForm] Guid? propertyId, IFormFileCollection files)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("Invalid user");

            string baseUrl = GetHostWithPort();
            var result = await _mediaServices.UploadPropertyImagesAsync(propertyId, files, Request.Scheme, baseUrl);
            return HandleResult(result);
        }

        /// <summary>
        /// Get the number of remaining edit times for a property
        /// </summary>
        /// <param name="propertyId">The ID of the property</param>
        /// <returns>The number of remaining edit times</returns>
        /// <response code="200">Returns the remaining edit times</response>
        /// <response code="401">If the user is not authenticated</response>
        [HttpGet("edit-remaining/{propertyId}")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> VerifyPropertyRemainingTimes(Guid propertyId)
        {
            var result = await _propertyService.VerifyPropertyRemainingTimes(propertyId);
            return HandleResult(result);
        }

        /// <summary>
        /// Get property count statistics for the current user
        /// </summary>
        /// <returns>Property count statistics</returns>
        /// <response code="200">Returns the property statistics</response>
        /// <response code="400">If the user is invalid</response>
        /// <response code="401">If the user is not authenticated</response>
        [HttpGet("stats")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> GetPropertyCountStats()
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("Invalid user");

            var result = await _propertyService.GetPropertyCountStatsByUserAsync(userId.Value);
            return HandleResult(result);
        }

        /// <summary>
        /// Search for properties based on various criteria
        /// </summary>
        /// <param name="postType">Filter by post type (e.g., Sell, Rent)</param>
        /// <param name="propertyType">Filter by property type (e.g., Apartment, House)</param>
        /// <param name="cityId">Filter by city ID</param>
        /// <param name="districtId">Filter by district ID</param>
        /// <param name="address">Filter by address text</param>
        /// <param name="minPrice">Filter by minimum price</param>
        /// <param name="maxPrice">Filter by maximum price</param>
        /// <param name="minArea">Filter by minimum area</param>
        /// <param name="maxArea">Filter by maximum area</param>
        /// <param name="minRooms">Filter by minimum number of rooms</param>
        /// <param name="minToilets">Filter by minimum number of toilets</param>
        /// <param name="direction">Filter by property direction</param>
        /// <param name="legality">Filter by property legality status</param>
        /// <param name="minRoadWidth">Filter by minimum road width</param>
        /// <param name="page">Page number for pagination</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <returns>Paged list of properties matching the criteria</returns>
        /// <response code="200">Returns the list of properties</response>
        /// <response code="500">If there was an error processing the request</response>
        [HttpGet("search")]
        [AllowAnonymous]
        public async Task<IActionResult> SearchProperties(
            [FromQuery] List<PostType>? postType = null,
            [FromQuery] List<PropertyType>? propertyType = null,
            [FromQuery] string? cityId = null,
            [FromQuery] string? districtId = null,
            [FromQuery] string? address = null,
            [FromQuery] decimal? minPrice = null,
            [FromQuery] decimal? maxPrice = null,
            [FromQuery] decimal? minArea = null,
            [FromQuery] decimal? maxArea = null,
            [FromQuery] int? minRooms = null,
            [FromQuery] int? minToilets = null,
            [FromQuery] string? direction = null,
            [FromQuery] string? legality = null,
            [FromQuery] decimal? minRoadWidth = null,
            [FromQuery] decimal? swLat = null,
            [FromQuery] decimal? swLng = null,
            [FromQuery] decimal? neLat = null,
            [FromQuery] decimal? neLng = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            var filterCriteria = new PropertyFilterCriteriaDto
            {
                PostTypes = postType,
                PropertyTypes = propertyType,
                CityId = cityId,
                DistrictId = districtId,
                Address = address,
                MinPrice = minPrice,
                MaxPrice = maxPrice,
                MinArea = minArea,
                MaxArea = maxArea,
                MinRooms = minRooms,
                MinToilets = minToilets,
                Direction = direction,
                Legality = legality,
                MinRoadWidth = minRoadWidth,
                NeLat = neLat,
                NeLng = neLng,
                SwLat = swLat,
                SwLng = swLng,
                Page = page,
                PageSize = pageSize,
                Status = _statusApproved
            };

            var result = await _propertyService.SearchPropertiesAsync(filterCriteria);
            return HandleResult(result);
        }

        /// <summary>
        /// Get properties near a specific location
        /// </summary>
        /// <param name="latitude">The latitude of the center point</param>
        /// <param name="longitude">The longitude of the center point</param>
        /// <param name="radius">The search radius in kilometers (default: 5km)</param>
        /// <param name="page">Page number for pagination</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <returns>Paged list of properties within the specified radius</returns>
        /// <response code="200">Returns the list of nearby properties</response>
        /// <response code="500">If there was an error processing the request</response>
        [HttpGet("nearby")]
        [AllowAnonymous]
        public async Task<IActionResult> GetNearbyProperties(
            [FromQuery] decimal? swLat = null,
            [FromQuery] decimal? swLng = null,
            [FromQuery] decimal? neLat = null,
            [FromQuery] decimal? neLng = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            var filterCriteria = new PropertyFilterCriteriaDto
            {
                SwLat = swLat,
                SwLng = swLng,
                NeLat = neLat,
                NeLng = neLng,
                Page = page,
                PageSize = pageSize,
                Status = _statusApproved
            };

            var result = await _propertyService.SearchPropertiesAsync(filterCriteria);
            return HandleResult(result);
        }


        /// <summary>
        /// Renew a property listing by extending its expiration date
        /// </summary>
        /// <param name="request">The renewal request containing property ID and duration</param>
        /// <returns>The updated property with new expiration date</returns>
        /// <response code="200">Returns the updated property</response>
        /// <response code="400">If the request is invalid or the user has insufficient funds</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="403">If the user is not authorized to renew this property</response>
        /// <response code="404">If the property is not found</response>
        [HttpPost("renew")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> RenewProperty([FromBody] PropertyRenewalDto request)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("Invalid user");

            var result = await _propertyService.RenewPropertyAsync(request, userId.Value);
            return HandleResult(result);
        }

        private async Task UpdateExistingMediaFiles(Guid propertyId, List<PropertyMediaDto> propertyMediaDtos)
        {
            var existingMediaResult = await _mediaServices.GetMediaByPropertyIdAsync(propertyId);
            if (!existingMediaResult.IsSuccess) return;

            var existingMedia = existingMediaResult.Value;
            var avatarMedia = propertyMediaDtos.FirstOrDefault(x => x.IsAvatar);
            if (avatarMedia != null)
            {
                foreach (var existingMediaItem in existingMedia)
                {
                    if (existingMediaItem.IsAvatar && existingMediaItem.Id != avatarMedia.Id)
                    {
                        var resetMediaDto = new PropertyMediaDto { Id = existingMediaItem.Id, IsAvatar = false };
                        await _mediaServices.UpdateMediaAsync(existingMediaItem.Id, propertyId, existingMediaItem.FilePath ?? string.Empty, resetMediaDto);
                    }
                }
            }
            foreach (var mediaDto in propertyMediaDtos)
            {
                var existingMediaItem = existingMedia.FirstOrDefault(x => x.Id == mediaDto.Id);
                if (existingMediaItem != null)
                {
                    await _mediaServices.UpdateMediaAsync(existingMediaItem.Id, propertyId, existingMediaItem.FilePath ?? string.Empty, mediaDto);
                }
            }
        }

        private async Task MoveTempImagesToPropertyFolder(Guid propertyId, List<Guid> imageIds, List<PropertyMediaDto> propertyMediaDtos)
        {
            // Get the property folder using the file storage service
            string propertyFolder = _fileStorageService.GetPropertyImageFolder(propertyId);

            // Ensure the property folder exists
            var folderResult = await _fileStorageService.EnsureFolderExistsAsync(propertyFolder);
            if (!folderResult.IsSuccess)
            {
                _logger.LogError("Failed to create property folder: {Error}", folderResult.ErrorMessage);
                return;
            }

            var tempImagesResult = await _mediaServices.GetAllMediaAsync(imageIds);
            if (!tempImagesResult.IsSuccess) return;

            var tempImages = tempImagesResult.Value;

            var avatarMedia = propertyMediaDtos?.FirstOrDefault(x => x.IsAvatar);
            if (avatarMedia != null)
            {
                var existingPropertyMediaResult = await _mediaServices.GetMediaByPropertyIdAsync(propertyId);
                if (existingPropertyMediaResult.IsSuccess && existingPropertyMediaResult.Value != null)
                {
                    foreach (var existingMedia in existingPropertyMediaResult.Value)
                    {
                        if (existingMedia.IsAvatar && existingMedia.Id != avatarMedia.Id)
                        {
                            var resetMediaDto = new PropertyMediaDto { Id = existingMedia.Id, IsAvatar = false };
                            await _mediaServices.UpdateMediaAsync(existingMedia.Id, propertyId, existingMedia.FilePath ?? string.Empty, resetMediaDto);
                        }
                    }
                }
            }

            foreach (var media in tempImages)
            {
                var tempMedia = propertyMediaDtos?.FirstOrDefault(x => x.Id == media.Id);
                string tempFilePath = media.FilePath ?? string.Empty;
                string newFileName = media.Id.ToString() + Path.GetExtension(media.FilePath);
                string newFilePath = Path.Combine(propertyFolder, newFileName);
                string newFilePathKey = newFilePath.Replace("\\", "/");

                // Use file storage service to check if file exists and move it
                if (await _fileStorageService.FileExistsAsync(tempFilePath))
                {
                    var moveResult = await _fileStorageService.MoveFileAsync(tempFilePath, newFilePath, true);

                    if (moveResult.IsSuccess)
                    {
                        if (tempMedia != null)
                        {
                            await _mediaServices.UpdateMediaAsync(media.Id, propertyId, moveResult?.Value ?? newFilePathKey, tempMedia);
                        }
                        else
                        {
                            var defaultMediaDto = new PropertyMediaDto { Id = media.Id, FilePath = newFilePath };
                            await _mediaServices.UpdateMediaAsync(media.Id, propertyId, moveResult?.Value ?? newFilePathKey, defaultMediaDto);
                        }
                    }
                    else
                    {
                        _logger.LogError("Failed to move file from {TempPath} to {NewPath}: {Error}", tempFilePath, newFilePath, moveResult.ErrorMessage);
                    }
                }
            }
        }

        private string GetHostWithPort()
        {
            var host = Request.Host.Host; // e.g., "staging.abc.com"
            var port = Request.Host.Port; // e.g., 8443, 80, 443, or null
            var scheme = Request.Scheme; // e.g., "http" or "https"

            // Check if the port should be included
            bool includePort = port.HasValue &&
                              !((scheme == "http" && port == 80) || (scheme == "https" && port == 443));

            var resultHost = includePort ? $"{host}:{port}" : host;

            _logger.LogInformation("GetHostWithPort returned: {Host}", resultHost);

            return resultHost;
        }
    }
}
