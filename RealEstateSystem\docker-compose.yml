services:
  
  # RealEstate.API
  yezhome-api:
    build:
      context: .
      dockerfile: RealEstate.API/Dockerfile
    container_name: yezhome-api
    environment:
      - ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT}
      - ASPNETCORE_URLS=http://+:8080
      # Fixed: Use consistent DB host variable
      - ConnectionStrings__YezHomeConnection=Host=${DB_HOST:-yezhome-postgres};Database=${POSTGRES_DB};Username=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
      # JWT Configuration
      - JWT__Key=${JWT_KEY}
      - JWT__Issuer=${JWT_ISSUER}
      - JWT__Audience=${JWT_AUDIENCE}
      # AWS S3 Configuration
      - AWS__S3__AccessKey=${AWS_S3_ACCESS_KEY}
      - AWS__S3__SecretKey=${AWS_S3_SECRET_KEY}
      - AWS__S3__Region=${AWS_S3_REGION}
      - AWS__S3__BucketName=${AWS_S3_BUCKET_NAME}
      # AWS SES Configuration
      - AWS__SES__AccessKey=${AWS_SES_ACCESS_KEY}
      - AWS__SES__SecretKey=${AWS_SES_SECRET_KEY}
      - AWS__SES__Region=${AWS_SES_REGION}
      - AWS__SES__FromEmail=${AWS_SES_FROM_EMAIL}
      - AWS__SES__FromName=${AWS_SES_FROM_NAME}
      # Storage Configuration
      - Storage__Provider=${STORAGE_PROVIDER:-Local}
    ports:
      - "5049:8080"
    # Removed empty depends_on - database is external
    volumes:
      - api_property_images:/app/PropertyImages
      - api_user_avatars:/app/UserAvatars
      - api_temp:/app/Temp
    networks:
      - yezhome-network

  # RealEstate.InternalAPI
  yezhome-internal-api:
    build:
      context: .
      dockerfile: RealEstate.InternalAPI/Dockerfile
    container_name: yezhome-internal-api
    environment:
      - ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT}
      - ASPNETCORE_URLS=http://+:8080
      # Fixed: Use same connection string name and DB host
      - ConnectionStrings__YezHomeConnection=Host=${DB_HOST:-yezhome-postgres};Database=${POSTGRES_DB};Username=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
      # JWT Configuration
      - JWT__Key=${INTERNAL_JWT_KEY}
      - JWT__Issuer=${INTERNAL_JWT_ISSUER}
      - JWT__Audience=${INTERNAL_JWT_AUDIENCE}
      # AWS SES Configuration (if needed)
      - AWS__SES__AccessKey=${AWS_SES_ACCESS_KEY}
      - AWS__SES__SecretKey=${AWS_SES_SECRET_KEY}
      - AWS__SES__Region=${AWS_SES_REGION}
      - AWS__SES__FromEmail=${AWS_SES_FROM_EMAIL}
      - AWS__SES__FromName=${AWS_SES_FROM_NAME}
      # AWS S3 Configuration
      - AWS__S3__AccessKey=${AWS_S3_ACCESS_KEY}
      - AWS__S3__SecretKey=${AWS_S3_SECRET_KEY}
      - AWS__S3__Region=${AWS_S3_REGION}
      - AWS__S3__BucketName=${AWS_S3_BUCKET_NAME}
    ports:
      - "5176:8080"
    # Removed empty depends_on - database is external
    networks:
      - yezhome-network

volumes:
  api_property_images:
  api_user_avatars:
  api_temp:

networks:
  yezhome-network:
    external: true
