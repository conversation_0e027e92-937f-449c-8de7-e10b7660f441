# Docker Setup Guide - Fixed Configuration

## Changes Made

The docker-compose files have been fixed with the following improvements:

### ✅ Fixed Issues:

1. **Standardized connection strings** - Both APIs now use `YezHomeConnection`
2. **Removed empty `depends_on` sections** - Database is external
3. **Added external network connectivity** - Connect to separate database
4. **Fixed environment variables** - Added all required configurations
5. **Corrected port mappings** - Production ports now map correctly to container ports
6. **Added volumes for file storage** - Persistent storage for uploads
7. **Consistent service naming** - Fixed naming inconsistencies

## Environment Setup

### Development Environment

Create a `.env` file in `YEZHome/RealEstateSystem/` with these variables:

```env
# ASP.NET Core Environment
ASPNETCORE_ENVIRONMENT=Development

# Database Configuration
DB_HOST=yezhome-postgres
POSTGRES_DB=yezhome_dev_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=dev_password

# JWT Configuration for API
JWT_KEY=development_jwt_secret_key_32_chars_min_12345678
JWT_ISSUER=yezhomeAPI-Dev
JWT_AUDIENCE=yezhomeUsers-Dev

# JWT Configuration for Internal API
INTERNAL_JWT_KEY=development_internal_jwt_secret_key_32_chars_min_12345678
INTERNAL_JWT_ISSUER=yezhomeInternalAPI-Dev
INTERNAL_JWT_AUDIENCE=yezhomeAdmins-Dev

# CORS Configuration for API
CORS_ORIGIN_0=http://localhost:3000
CORS_ORIGIN_1=http://localhost:3001

# CORS Configuration for Internal API
INTERNAL_CORS_ORIGIN_0=http://localhost:4000
INTERNAL_CORS_ORIGIN_1=http://localhost:4001

# Storage Configuration
STORAGE_PROVIDER=Local

# AWS S3 Configuration (if using S3)
AWS_S3_ACCESS_KEY=your_s3_access_key
AWS_S3_SECRET_KEY=your_s3_secret_key
AWS_S3_REGION=us-east-1
AWS_S3_BUCKET_NAME=yezhome-dev-media

# AWS SES Configuration (for email)
AWS_SES_ACCESS_KEY=your_ses_access_key
AWS_SES_SECRET_KEY=your_ses_secret_key
AWS_SES_REGION=us-east-1
AWS_SES_FROM_EMAIL=noreply@localhost
AWS_SES_FROM_NAME=YEZHome Development

# Internal Email Configuration
INTERNAL_EMAIL_NO_REPLY=noreply@localhost
INTERNAL_EMAIL_SUPPORT=support@localhost
INTERNAL_EMAIL_REVIEW_POST=review@localhost
```

### Production Environment

Create a `.env` file in `YEZHome_Deploy/` with these variables:

```env
# Database Configuration
DB_HOST=your-production-database-host
POSTGRES_DB=yezhome
POSTGRES_USER=your_prod_user
POSTGRES_PASSWORD=your_secure_production_password

# JWT Configuration for API (CHANGE THESE IN PRODUCTION!)
JWT_KEY=your_production_jwt_key_minimum_32_characters_secure_random_string
JWT_ISSUER=https://your-api-domain.com
JWT_AUDIENCE=https://your-frontend-domain.com

# JWT Configuration for Internal API (CHANGE THESE IN PRODUCTION!)
INTERNAL_JWT_KEY=your_production_internal_jwt_key_minimum_32_characters_secure_random_string
INTERNAL_JWT_ISSUER=https://your-internal-api-domain.com
INTERNAL_JWT_AUDIENCE=https://your-admin-frontend-domain.com

# CORS Configuration for API
CORS_ORIGIN_0=https://your-frontend-domain.com
CORS_ORIGIN_1=https://your-other-allowed-domain.com

# CORS Configuration for Internal API
INTERNAL_CORS_ORIGIN_0=https://your-admin-frontend-domain.com
INTERNAL_CORS_ORIGIN_1=https://your-other-admin-domain.com

# Storage Configuration
STORAGE_PROVIDER=S3

# AWS S3 Configuration (REQUIRED for production)
AWS_S3_ACCESS_KEY=your_production_s3_access_key
AWS_S3_SECRET_KEY=your_production_s3_secret_key
AWS_S3_REGION=us-east-1
AWS_S3_BUCKET_NAME=yezhome-prod-media

# AWS SES Configuration (for email)
AWS_SES_ACCESS_KEY=your_production_ses_access_key
AWS_SES_SECRET_KEY=your_production_ses_secret_key
AWS_SES_REGION=us-east-1
AWS_SES_FROM_EMAIL=<EMAIL>
AWS_SES_FROM_NAME=YEZHome

# Internal Email Configuration
INTERNAL_EMAIL_NO_REPLY=<EMAIL>
INTERNAL_EMAIL_SUPPORT=<EMAIL>
INTERNAL_EMAIL_REVIEW_POST=<EMAIL>

# Frontend Configuration
NEXT_PUBLIC_API_URL=https://your-api-domain.com
NEXT_PUBLIC_INTERNAL_API_URL=https://your-internal-api-domain.com
```

## Network Setup

### 1. Network is Auto-Created

The database docker-compose.yml now creates the `yezhome-network` network automatically, so **no manual network creation is needed**. The API services will connect to this network when started.

### 2. Start Database First

In `YEZHome_DB/` directory:

```bash
# Start the database
docker-compose up -d

# Verify database is running
docker-compose ps
```

### 3. Start API Services

In `YEZHome/RealEstateSystem/` directory:

```bash
# Development
docker-compose up -d

# Production (from YEZHome_Deploy/)
docker-compose up -d
```

The API services will automatically connect to the database network created in step 2.

## Port Mappings

### Development:
- **API**: http://localhost:5049
- **Internal API**: http://localhost:5176
- **Database**: localhost:5433 (from override)

### Production:
- **API**: http://localhost:5000
- **Internal API**: http://localhost:5001
- **Frontend**: http://localhost:3000

## Troubleshooting

### Network Issues:
```bash
# Check if network exists (should be created by database compose)
docker network ls | grep yezhome_db

# Check container connectivity
docker exec -it yezhome-api ping yezhome-postgres

# If network issues, restart database first, then API services
```

### Database Connection Issues:
```bash
# Check database is running
docker ps | grep postgres

# Check logs
docker logs yezhome-postgres

# Test connection from API container
docker exec -it yezhome-api bash
# Inside container: telnet yezhome-postgres 5432
```

### Environment Variable Issues:
```bash
# Check if .env file exists and is properly formatted
cat .env

# View container environment
docker exec -it yezhome-api env | grep ConnectionStrings
```

## Security Notes

⚠️ **Important for Production:**

1. **Change all default passwords and keys**
2. **Use strong JWT secrets (minimum 32 characters)**
3. **Configure proper CORS origins**
4. **Use environment files for sensitive data**
5. **Consider using Docker secrets for production deployments**
6. **Enable HTTPS in production**
7. **Restrict database access to specific IPs**

## File Storage

- **Development**: Files stored in local Docker volumes
- **Production**: Configured to use AWS S3 by default
- **Volumes**: `api_property_images`, `api_user_avatars`, `api_temp`

The fixed configuration now properly separates the database from the API services while maintaining connectivity through Docker networks.
