# Database Configuration
POSTGRES_DB=yezhome_db
POSTGRES_USER=yezhome_postgres_ad
POSTGRES_PASSWORD=your_secure_password_here

# JWT Configuration
JWT_KEY=your_jwt_secret_key_here_minimum_32_characters_required
JWT_ISSUER=yezhomeAPI
JWT_AUDIENCE=yezhomeUsers

# Internal API JWT Configuration
INTERNAL_JWT_KEY=your_internal_jwt_secret_key_here_minimum_32_characters
INTERNAL_JWT_ISSUER=yezhomeInternalAPI
INTERNAL_JWT_AUDIENCE=yezhomeAdmins

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,https://yezhome.vn
INTERNAL_CORS_ALLOWED_ORIGINS=http://localhost:3001,https://admin.yezhome.vn

# Storage Configuration
STORAGE_PROVIDER=S3

# AWS S3 Configuration
AWS_S3_ACCESS_KEY=your_s3_access_key
AWS_S3_SECRET_KEY=your_s3_secret_key
AWS_S3_REGION=ap-southeast-1
AWS_S3_BUCKET_NAME=yezhome-uat-bucket

# AWS SES Configuration (for email)
AWS_SES_ACCESS_KEY=your_ses_access_key
AWS_SES_SECRET_KEY=your_ses_secret_key
AWS_SES_REGION=ap-southeast-1
AWS_SES_FROM_EMAIL=<EMAIL>
AWS_SES_FROM_NAME=YEZ Home

# Internal Email Configuration
INTERNAL_EMAIL_NOREPLY=<EMAIL>
INTERNAL_EMAIL_SUPPORT=<EMAIL>
INTERNAL_EMAIL_REVIEW=<EMAIL>

# Application Environment
ASPNETCORE_ENVIRONMENT=Production