﻿using Microsoft.EntityFrameworkCore;
using RealEstate.Domain.Entities;

namespace RealEstate.Infrastructure
{
    public class ApplicationDbContext : DbContext
    {
        #region Core Entities
        public DbSet<AppUser> AppUser { get; set; }
        public DbSet<AdminRole> AdminRole { get; set; }
        public DbSet<UserRole> UserRole { get; set; }
        #endregion

        #region Property Related Entities
        public DbSet<Property> Property { get; set; }
        public DbSet<PropertyMedia> PropertyMedia { get; set; }
        public DbSet<PropertyStatusLog> PropertyStatusLog { get; set; }
        public DbSet<UserFavorite> UserFavorite { get; set; }
        #endregion

        #region Location Entities
        public DbSet<City> City { get; set; }
        public DbSet<District> District { get; set; }
        public DbSet<Ward> Ward { get; set; }
        public DbSet<Street> Street { get; set; }
        public DbSet<Project> Project { get; set; }
        #endregion

        #region Blog Entities
        public DbSet<BlogPost> BlogPost { get; set; }
        public DbSet<BlogComment> BlogComment { get; set; }
        #endregion

        #region Financial Entities
        public DbSet<Wallet> Wallets { get; set; }
        public DbSet<WalletTransaction> WalletTransactions { get; set; }
        public DbSet<MemberRanking> MemberRankings { get; set; }
        public DbSet<HighlightFee> HighlightFees { get; set; }
        #endregion

        #region Communication Entities
        public DbSet<ContactRequest> ContactRequest { get; set; }
        public DbSet<Notification> Notification { get; set; }
        public DbSet<NotificationPreference> NotificationPreferences { get; set; }
        #endregion

        #region User Profile Entities
        public DbSet<UserAvatar> UserAvatar { get; set; }
        #endregion

        #region Permission System
        public DbSet<Permission> Permission { get; set; }
        public DbSet<RolePermission> RolePermission { get; set; }
        #endregion

        #region Property Analytics
        public DbSet<PropertyEngagementView> PropertyEngagementView { get; set; }
        public DbSet<PropertyEngagementSummary> PropertyEngagementSummary { get; set; }
        public DbSet<PropertyEngagementEvent> PropertyEngagementEvents { get; set; }
        #endregion

        public DbSet<Setting> Setting { get; set; }
        public DbSet<PasswordResetToken> PasswordResetToken { get; set; }

        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
            // Updated for Npgsql 9.0+ compatibility
            AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", false);
            AppContext.SetSwitch("Npgsql.DisableDateTimeInfinityConversions", true);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure entity relationships and constraints
            ConfigureUserEntities(modelBuilder);
            ConfigurePropertyEntities(modelBuilder);
            ConfigureLocationEntities(modelBuilder);
            ConfigureBlogEntities(modelBuilder);
            ConfigureFinancialEntities(modelBuilder);
            ConfigureCommunicationEntities(modelBuilder);
            ConfigurePermissionEntities(modelBuilder);
            ConfigureAnalyticsEntities(modelBuilder);
            ConfigureSettingEntities(modelBuilder);

            // Apply global query filters
            ApplyGlobalQueryFilters(modelBuilder);
        }

        #region Entity Configuration Methods

        private void ConfigureUserEntities(ModelBuilder modelBuilder)
        {
            // AppUser DateTime conversions
            ConfigureUtcDateTimeConversions<AppUser>(modelBuilder);

            // AppUser LastLogin DateTime conversion - simplified
            modelBuilder.Entity<AppUser>()
                .Property(u => u.LastLogin)
                .HasConversion(
                    src => src.HasValue ? DateTime.SpecifyKind(src.Value, DateTimeKind.Utc) : src,
                    dst => dst.HasValue ? DateTime.SpecifyKind(dst.Value, DateTimeKind.Utc) : dst
                );

            modelBuilder.Entity<AppUser>(entity =>
            {
                entity.Property(e => e.UserType)
                      .HasConversion<string>()
                      .HasMaxLength(20);
            });

            // UserRole relationships
            modelBuilder.Entity<UserRole>()
                .HasOne(ur => ur.User)
                .WithMany(u => u.UserRoles)
                .HasForeignKey(ur => ur.UserID)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<UserRole>()
                .HasOne(ur => ur.Role)
                .WithMany(r => r.UserRoles)
                .HasForeignKey(ur => ur.RoleID)
                .OnDelete(DeleteBehavior.Cascade);

            // UserAvatar relationship
            modelBuilder.Entity<UserAvatar>()
                .HasOne(ua => ua.User)
                .WithMany()
                .HasForeignKey(ua => ua.UserID)
                .OnDelete(DeleteBehavior.Cascade);
        }

        private void ConfigurePropertyEntities(ModelBuilder modelBuilder)
        {
            ConfigureUtcDateTimeConversions<Property>(modelBuilder);

            // Property relationships
            modelBuilder.Entity<Property>()
                .HasOne(p => p.Owner)
                .WithMany(u => u.Properties)
                .HasForeignKey(p => p.OwnerID)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Property>()
                .Property(p => p.Code)
                .ValueGeneratedOnAdd();

            modelBuilder.Entity<Property>(entity =>
            {
                entity.Property(e => e.PostType)
                      .HasConversion<string>()
                      .HasMaxLength(5);

                entity.Property(e => e.PropertyType)
                      .HasConversion<string>()
                      .HasMaxLength(20);

                entity.Property(e => e.Status)
                      .HasConversion<string>()
                      .HasMaxLength(20);
            });

            // PropertyMedia relationships
            modelBuilder.Entity<PropertyMedia>()
                .HasOne(pm => pm.Property)
                .WithMany(p => p.PropertyMedia)
                .HasForeignKey(pm => pm.PropertyID)
                .OnDelete(DeleteBehavior.Cascade);

            // PropertyStatusLog relationships
            modelBuilder.Entity<PropertyStatusLog>()
                .HasOne(psl => psl.Property)
                .WithMany(p => p.PropertyStatusLogs)
                .HasForeignKey(psl => psl.PropertyID)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<PropertyStatusLog>()
                .HasOne(psl => psl.ChangedByUser)
                .WithMany()
                .HasForeignKey(psl => psl.ChangedBy)
                .OnDelete(DeleteBehavior.Restrict);

            // UserFavorite relationships
            modelBuilder.Entity<UserFavorite>()
                .HasOne(uf => uf.User)
                .WithMany(u => u.UserFavorites)
                .HasForeignKey(uf => uf.UserID)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<UserFavorite>()
                .HasOne(uf => uf.Property)
                .WithMany(p => p.UserFavorites)
                .HasForeignKey(uf => uf.PropertyID)
                .OnDelete(DeleteBehavior.Cascade);

            // Unique constraint for UserFavorite
            modelBuilder.Entity<UserFavorite>()
                .HasIndex(uf => new { uf.UserID, uf.PropertyID })
                .IsUnique();
        }

        private void ConfigureLocationEntities(ModelBuilder modelBuilder)
        {
            // City configuration
            modelBuilder.Entity<City>()
                .HasKey(c => c.Id);

            // District relationships
            modelBuilder.Entity<District>()
                .HasOne(d => d.City)
                .WithMany(c => c.Districts)
                .HasForeignKey(d => d.CityId)
                .OnDelete(DeleteBehavior.Cascade);

            // Ward relationships
            modelBuilder.Entity<Ward>()
                .HasOne(w => w.District)
                .WithMany(d => d.Wards)
                .HasForeignKey(w => w.DistrictId)
                .OnDelete(DeleteBehavior.Cascade);

            // Street relationships
            modelBuilder.Entity<Street>()
                .HasOne(s => s.District)
                .WithMany(d => d.Streets)
                .HasForeignKey(s => s.DistrictId)
                .OnDelete(DeleteBehavior.Cascade);

            // Project relationships
            modelBuilder.Entity<Project>()
                .HasOne(p => p.Ward)
                .WithMany(w => w.Projects)
                .HasForeignKey(p => p.WardId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Project>()
                .HasOne(p => p.Street)
                .WithMany(s => s.Projects)
                .HasForeignKey(p => p.StreetId)
                .OnDelete(DeleteBehavior.SetNull);
        }

        private void ConfigureBlogEntities(ModelBuilder modelBuilder)
        {
            ConfigureUtcDateTimeConversions<BlogPost>(modelBuilder);

            // BlogPost relationships
            modelBuilder.Entity<BlogPost>()
                .HasOne(bp => bp.Author)
                .WithMany(u => u.BlogPosts)
                .HasForeignKey(bp => bp.AuthorID)
                .OnDelete(DeleteBehavior.Cascade);

            // BlogComment relationships
            modelBuilder.Entity<BlogComment>()
                .HasOne(bc => bc.Post)
                .WithMany(bp => bp.BlogComments)
                .HasForeignKey(bc => bc.PostID)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<BlogComment>()
                .HasOne(bc => bc.User)
                .WithMany(u => u.BlogComments)
                .HasForeignKey(bc => bc.UserID)
                .OnDelete(DeleteBehavior.Cascade);
        }

        private void ConfigureFinancialEntities(ModelBuilder modelBuilder)
        {
            // Wallet relationships
            modelBuilder.Entity<Wallet>()
                .HasOne(w => w.User)
                .WithMany()
                .HasForeignKey(w => w.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            // WalletTransaction relationships
            modelBuilder.Entity<WalletTransaction>()
                .HasOne(wt => wt.User)
                .WithMany()
                .HasForeignKey(wt => wt.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<WalletTransaction>()
                .Property(u => u.CreatedAt)
                .HasConversion(
                    src => DateTime.SpecifyKind(src, DateTimeKind.Utc),
                    dst => DateTime.SpecifyKind(dst, DateTimeKind.Utc)
                );

            // MemberRanking configuration
            modelBuilder.Entity<MemberRanking>()
                .HasKey(mr => mr.RankName);

            // HighlightFee relationships
            modelBuilder.Entity<HighlightFee>()
                .HasOne(hf => hf.MemberRanking)
                .WithOne(mr => mr.HighlightFee)
                .HasForeignKey<HighlightFee>(hf => hf.RankName)
                .OnDelete(DeleteBehavior.Cascade);

            // Cấu hình cột RowVersion là một concurrency token
            modelBuilder.Entity<Wallet>()
                .Property(w => w.RowVersion)
                .IsConcurrencyToken();
        }

        private void ConfigureCommunicationEntities(ModelBuilder modelBuilder)
        {
            // ContactRequest relationships
            modelBuilder.Entity<ContactRequest>()
                .HasOne(cr => cr.Property)
                .WithMany()
                .HasForeignKey(cr => cr.PropertyId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<ContactRequest>()
                .HasOne(cr => cr.Agent)
                .WithMany()
                .HasForeignKey(cr => cr.PropertyOwnerId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<ContactRequest>()
                .HasOne(cr => cr.User)
                .WithMany()
                .HasForeignKey(cr => cr.UserId)
                .OnDelete(DeleteBehavior.SetNull);

            // Notification relationships
            modelBuilder.Entity<Notification>()
                .HasOne(n => n.User)
                .WithMany()
                .HasForeignKey(n => n.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Notification>(entity =>
            {
                entity.Property(e => e.Category)
                      .HasConversion<string>()
                      .HasMaxLength(50);

                entity.Property(e => e.Type)
                      .HasConversion<string>()
                      .HasMaxLength(50);
            });

            // NotificationPreference relationships
            modelBuilder.Entity<NotificationPreference>()
                .HasOne(np => np.User)
                .WithMany()
                .HasForeignKey(np => np.UserId)
                .OnDelete(DeleteBehavior.Cascade);
        }

        private void ConfigurePermissionEntities(ModelBuilder modelBuilder)
        {
            // RolePermission relationships
            modelBuilder.Entity<RolePermission>()
                .HasOne(rp => rp.Role)
                .WithMany(r => r.RolePermissions)
                .HasForeignKey(rp => rp.RoleID)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<RolePermission>()
                .HasOne(rp => rp.Permission)
                .WithMany(p => p.RolePermissions)
                .HasForeignKey(rp => rp.PermissionID)
                .OnDelete(DeleteBehavior.Cascade);

            // Unique constraint for RolePermission
            modelBuilder.Entity<RolePermission>()
                .HasIndex(rp => new { rp.RoleID, rp.PermissionID })
                .IsUnique();
        }

        private void ConfigureAnalyticsEntities(ModelBuilder modelBuilder)
        {
            // PropertyEngagementView relationships
            modelBuilder.Entity<PropertyEngagementView>()
                .HasOne(pvl => pvl.Property)
                .WithMany()
                .HasForeignKey(pvl => pvl.PropertyId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<PropertyEngagementView>()
                .HasOne(pvl => pvl.Viewer)
                .WithMany()
                .HasForeignKey(pvl => pvl.ViewerId)
                .OnDelete(DeleteBehavior.SetNull);



            // PropertyEngagementSummary relationships
            modelBuilder.Entity<PropertyEngagementSummary>()
                .HasOne(pes => pes.Property)
                .WithOne()
                .HasForeignKey<PropertyEngagementSummary>(pes => pes.PropertyId)
                .OnDelete(DeleteBehavior.Cascade);

            // PropertyEngagementEvent relationships
            modelBuilder.Entity<PropertyEngagementEvent>()
                .HasOne(pee => pee.Property)
                .WithMany()
                .HasForeignKey(pee => pee.PropertyId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<PropertyEngagementEvent>()
                .HasOne(pee => pee.User)
                .WithMany()
                .HasForeignKey(pee => pee.UserId)
                .OnDelete(DeleteBehavior.SetNull);

            // PropertyEngagementEvent UTC DateTime conversion
            modelBuilder.Entity<PropertyEngagementEvent>()
                .Property(pee => pee.CreatedAt)
                .HasConversion(
                    src => src.Kind == DateTimeKind.Utc ? src : DateTime.SpecifyKind(src, DateTimeKind.Utc),
                    dst => dst.Kind == DateTimeKind.Utc ? dst : DateTime.SpecifyKind(dst, DateTimeKind.Utc)
                );
        }

        private void ConfigureSettingEntities(ModelBuilder modelBuilder)
        {
            // Setting relationships
            modelBuilder.Entity<Setting>()
                .HasKey(s => s.Key);
            modelBuilder.Entity<Setting>()
                .Property(s => s.Value)
                .IsRequired();

            modelBuilder.Entity<PasswordResetToken>()
                .HasKey(s => s.Id);
        }

        #endregion

        #region Helper Methods

        private void ConfigureUtcDateTimeConversions<T>(ModelBuilder modelBuilder) where T : BaseEntityWithAuditable
        {
            // CreatedAt conversion
            modelBuilder.Entity<T>()
                .Property(e => e.CreatedAt)
                .HasConversion(
                    src => src.Kind == DateTimeKind.Utc ? src : DateTime.SpecifyKind(src, DateTimeKind.Utc),
                    dst => dst.Kind == DateTimeKind.Utc ? dst : DateTime.SpecifyKind(dst, DateTimeKind.Utc)
                );

            // UpdatedAt conversion
            modelBuilder.Entity<T>()
                .Property(e => e.UpdatedAt)
                .HasConversion(
                    src => src.HasValue ? (src.Value.Kind == DateTimeKind.Utc ? src.Value : DateTime.SpecifyKind(src.Value, DateTimeKind.Utc)) : src,
                    dst => dst.HasValue ? (dst.Value.Kind == DateTimeKind.Utc ? dst.Value : DateTime.SpecifyKind(dst.Value, DateTimeKind.Utc)) : dst
                );

            // DeletedAt conversion
            modelBuilder.Entity<T>()
                .Property(e => e.DeletedAt)
                .HasConversion(
                    src => src.HasValue ? (src.Value.Kind == DateTimeKind.Utc ? src.Value : DateTime.SpecifyKind(src.Value, DateTimeKind.Utc)) : src,
                    dst => dst.HasValue ? (dst.Value.Kind == DateTimeKind.Utc ? dst.Value : DateTime.SpecifyKind(dst.Value, DateTimeKind.Utc)) : dst
                );
        }

        private void ApplyGlobalQueryFilters(ModelBuilder modelBuilder)
        {
            // Soft delete filters for auditable entities
            modelBuilder.Entity<AppUser>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<Property>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<BlogPost>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<BlogComment>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<ContactRequest>().HasQueryFilter(e => !e.IsDeleted);

            // Filters for entities with user relationships
            modelBuilder.Entity<UserRole>().HasQueryFilter(ur => !ur.User.IsDeleted);
            modelBuilder.Entity<UserAvatar>().HasQueryFilter(ua => ua.User != null && !ua.User.IsDeleted);
            modelBuilder.Entity<Wallet>().HasQueryFilter(w => w.User != null && !w.User.IsDeleted);
            modelBuilder.Entity<WalletTransaction>().HasQueryFilter(wt => wt.User != null && !wt.User.IsDeleted);
            modelBuilder.Entity<NotificationPreference>().HasQueryFilter(np => np.User != null && !np.User.IsDeleted);

            // Filters for entities with property relationships
            modelBuilder.Entity<UserFavorite>().HasQueryFilter(uf => uf.Property != null && !uf.Property.IsDeleted);
            modelBuilder.Entity<PropertyStatusLog>().HasQueryFilter(psl => psl.Property != null && !psl.Property.IsDeleted && psl.ChangedByUser != null && !psl.ChangedByUser.IsDeleted);
            modelBuilder.Entity<PropertyEngagementView>().HasQueryFilter(pvl => pvl.Property != null && !pvl.Property.IsDeleted && (pvl.Viewer == null || !pvl.Viewer.IsDeleted));
            modelBuilder.Entity<PropertyEngagementSummary>().HasQueryFilter(pes => pes.Property != null && !pes.Property.IsDeleted);

            modelBuilder.Entity<PropertyEngagementEvent>().HasQueryFilter(pee => pee.Property != null && !pee.Property.IsDeleted && (pee.User == null || !pee.User.IsDeleted));
        }

        #endregion
    }
}
