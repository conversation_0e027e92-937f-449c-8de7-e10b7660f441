using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.Interfaces;

namespace RealEstate.API.Controllers
{
    [Route("[controller]")]
    public class UserAvatarMediaController : Controller
    {
        private readonly IUserAvatarService _userAvatarService;

        public UserAvatarMediaController(IUserAvatarService userAvatarService)
        {
            _userAvatarService = userAvatarService;
        }

        [HttpGet("{fileId}")]
        [AllowAnonymous]
        public async Task<IActionResult> GetMedia(Guid fileId, [FromQuery] string? size = null)
        {
            var userAvatar = await _userAvatarService.GetUserAvatarByIdAsync(fileId);
            if (userAvatar == null)
            {
                return NotFound(new { Message = "Avatar not found" });
            }

            string filePath = userAvatar?.FilePath ?? string.Empty;

            if (string.IsNullOrEmpty(filePath) || !System.IO.File.Exists(filePath))
            {
                return NotFound(new { Message = "Avatar file not found" });
            }

            var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
            return File(fileStream, userAvatar.MediaType!);
        }
    }
}
