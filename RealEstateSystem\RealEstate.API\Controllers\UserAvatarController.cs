using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.API.Controllers;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using System.Security.Claims;

namespace RealEstate.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class UserAvatarController : BaseController
    {
        private readonly IUserService _userService;
        private readonly IUserAvatarService _userAvatarService;
        private readonly ILogger<UserAvatarController> _logger;

        public UserAvatarController(
            IUserService userService,
            IUserAvatarService userAvatarService,
            ILogger<UserAvatarController> logger)
        {
            _userService = userService;
            _userAvatarService = userAvatarService;
            _logger = logger;
        }

        [HttpPost("upload")]
        [Authorize(Policy = "UserExists")]
        public async Task<ActionResult<UserDto>> UploadAvatar(IFormFile file)
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest(new { Message = "No file uploaded." });
            }

            var userId = GetUserId();
            if (userId == null)
            {
                return BadRequest(new { Message = "Invalid user" });
            }

            // Skip non-image files
            if (!file.ContentType.StartsWith("image/"))
            {
                return BadRequest(new { Message = "Only image files are allowed." });
            }

            // Generate a unique file ID (UUID-based)
            var fileId = Guid.NewGuid();
            var extension = Path.GetExtension(file.FileName);

            // Create user-specific folder for avatars
            string userFolder = Path.Combine("UserAvatars", userId.Value.ToString());
            if (!Directory.Exists(userFolder))
            {
                Directory.CreateDirectory(userFolder);
            }

            // Define the direct file path
            var filePath = Path.Combine(userFolder, $"{fileId}{extension}");

            // Save the file directly to the user's folder
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            // Generate a public URL
            string hostWithPort = GetHostWithPort();
            string publicUrl = $"{Request.Scheme}://{hostWithPort}/useravatarmedia/{fileId}";

            _logger.LogInformation("Avatar uploaded. FileNameUpload: {FileName} FileId: {FileId}, PublicURL: {PublicURL}", file.FileName, fileId, publicUrl);

            // Store metadata in the database
            var userAvatar = new CreateUserAvatarDto
            {
                Id = fileId,
                UserID = userId.Value,
                MediaType = file.ContentType,
                MediaURL = publicUrl,
                FilePath = filePath,
                UploadedAt = DateTime.UtcNow
            };

            await _userAvatarService.CreateUserAvatarAsync(userAvatar);

            // Get updated user data
            var updatedUser = await _userService.GetUserByIdAsync(userId.Value);

            return Ok(updatedUser);
        }

        [HttpGet]
        [Authorize(Policy = "UserExists")]
        public async Task<ActionResult<UserAvatarDto>> GetAvatarImage()
        {
            var userId = GetUserId();
            if (userId == null)
            {
                return BadRequest(new { Message = "Invalid user" });
            }

            var userAvatar = await _userAvatarService.GetUserAvatarByUserIdAsync(userId.Value);
            if (userAvatar == null)
            {
                return NotFound(new { Message = "No avatar image found for this user." });
            }

            return Ok(userAvatar);
        }

        [HttpDelete]
        [Authorize(Policy = "UserExists")]
        public async Task<ActionResult> RemoveAvatar()
        {
            var userId = GetUserId();
            if (userId == null)
            {
                return BadRequest(new { Message = "Invalid user" });
            }

            // Get the user's avatar
            var userAvatar = await _userAvatarService.GetUserAvatarByUserIdAsync(userId.Value);
            if (userAvatar != null)
            {
                // Delete the avatar
                await _userAvatarService.DeleteUserAvatarAsync(userAvatar.Id);
            }

            return NoContent();
        }

        private string GetHostWithPort()
        {
            var host = Request.Host.Host; // e.g., "staging.abc.com"
            var port = Request.Host.Port; // e.g., 8443, 80, 443, or null
            var scheme = Request.Scheme; // e.g., "http" or "https"

            bool includePort = port.HasValue && !((scheme == "http" && port == 80) || (scheme == "https" && port == 443));

            var resultHost = includePort ? $"{host}:{port}" : host;

            _logger.LogInformation("GetHostWithPort returned: Host: {host} - Port: {port}, Scheme: {scheme}", host, port, scheme);

            return resultHost;
        }
    }
}
