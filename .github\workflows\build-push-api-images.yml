name: 📦 Build & Push API Images

on:
  push:
    branches:
      - main      # Auto-deploy to staging
  workflow_dispatch:

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set environment variables
        id: vars
        run: |
          echo "repo_owner_lower=${GITHUB_REPOSITORY_OWNER,,}" >> $GITHUB_ENV
          
          # Main branch always builds latest and deploys to staging first
          echo "ENVIRONMENT=staging" >> $GITHUB_ENV
          echo "IMAGE_TAG=latest" >> $GITHUB_ENV
          echo "DEPLOY_ENVIRONMENT=staging" >> $GITHUB_ENV
          echo "BUILD_NUMBER=${GITHUB_RUN_NUMBER}" >> $GITHUB_ENV

      # Build & Push RealEstate.API with caching
      - name: Build and push RealEstate.API image
        uses: docker/build-push-action@v5
        with:
          context: RealEstateSystem
          file: RealEstateSystem/RealEstate.API/Dockerfile
          push: true
          tags: |
            ghcr.io/${{ env.repo_owner_lower }}/yezhome-api:latest
            ghcr.io/${{ env.repo_owner_lower }}/yezhome-api:build-${{ env.BUILD_NUMBER }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64

      # Build & Push RealEstate.InternalAPI with caching
      - name: Build and push RealEstate.InternalAPI image
        uses: docker/build-push-action@v5
        with:
          context: RealEstateSystem
          file: RealEstateSystem/RealEstate.InternalAPI/Dockerfile
          push: true
          tags: |
            ghcr.io/${{ env.repo_owner_lower }}/yezhome-internal-api:latest
            ghcr.io/${{ env.repo_owner_lower }}/yezhome-internal-api:build-${{ env.BUILD_NUMBER }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64

      # Cleanup old GHCR images (keep only 5 latest build versions)
      - name: Cleanup old GHCR images
        run: |
          echo "🧹 Cleaning up old GHCR images (keeping latest 5 build versions)..."
          
          # Function to cleanup old images for a repository
          cleanup_old_images() {
            local repo_name=$1
            echo "🔍 Cleaning up old images for $repo_name..."
            
            # Wait a moment to ensure new packages are visible
            sleep 10
            
            # Get all package versions with build tags, sorted by creation date (newest first)
            echo "📋 Getting package versions for $repo_name..."
            all_versions=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
              -H "Accept: application/vnd.github+json" \
              "https://api.github.com/users/${{ env.repo_owner_lower }}/packages/container/$repo_name/versions")
            
            # Debug: Show what we got
            echo "📊 Total versions found: $(echo "$all_versions" | jq '. | length')"
            
            # Get versions with build-* tags, sorted by created_at (newest first), skip first 5
            versions_to_delete=$(echo "$all_versions" | \
              jq -r '.[] | select(.metadata.container.tags[]? | test("^build-[0-9]+$")) | 
                     {id: .id, created: .created_at, tags: .metadata.container.tags}' | \
              jq -s 'sort_by(.created) | reverse | .[5:] | .[].id')
            
            if [ -z "$versions_to_delete" ] || [ "$versions_to_delete" = "null" ]; then
              echo "ℹ️  No old build versions to delete for $repo_name (keeping latest 5)"
              return
            fi
            
            echo "🗑️  Found $(echo "$versions_to_delete" | wc -l) old versions to delete"
            
            # Delete old versions
            echo "$versions_to_delete" | while read -r version_id; do
              if [ -n "$version_id" ] && [ "$version_id" != "null" ]; then
                echo "🗑️  Deleting version ID: $version_id"
                delete_response=$(curl -s -w "%{http_code}" -X DELETE \
                  -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
                  -H "Accept: application/vnd.github+json" \
                  "https://api.github.com/users/${{ env.repo_owner_lower }}/packages/container/$repo_name/versions/$version_id")
                
                http_code="${delete_response: -3}"
                if [ "$http_code" = "204" ]; then
                  echo "✅ Successfully deleted version $version_id"
                else
                  echo "⚠️  Failed to delete version $version_id (HTTP: $http_code)"
                fi
              fi
            done
            
            echo "✅ Cleanup completed for $repo_name"
          }
          
          # Only run cleanup if we have more than 5 builds to avoid deleting everything
          echo "⏳ Waiting for new packages to be fully available..."
          sleep 5
          
          # Cleanup both repositories
          cleanup_old_images "yezhome-api"
          cleanup_old_images "yezhome-internal-api"
          
          echo "🎉 All cleanup operations completed!"

      # Auto-deploy to Staging
      - name: Auto-deploy to Staging
        run: |
          echo "🚀 Auto-deploying to staging environment..."
          response=$(curl -s -w "%{http_code}" -X POST \
            -H "Accept: application/vnd.github+json" \
            -H "Authorization: token ${{ secrets.DEPLOY_PAT }}" \
            https://api.github.com/repos/${{ env.repo_owner_lower }}/YEZHome_Deploy/dispatches \
            -d '{
              "event_type": "deploy-staging",
              "client_payload": {
                "source": "YEZHome",
                "environment": "staging",
                "deploy_environment": "staging",
                "branch": "${{ github.ref_name }}",
                "commit_sha": "${{ github.sha }}",
                "build_number": "${{ env.BUILD_NUMBER }}",
                "image_tag": "latest",
                "auto_deploy": true
              }
            }')
          
          http_code="${response: -3}"
          if [ "$http_code" -eq 204 ]; then
            echo "✅ Staging deployment triggered successfully!"
            echo "🎯 Ready for manual production deployment via GitHub Actions UI"
          else
            echo "❌ Failed to trigger staging deployment. HTTP code: $http_code"
            echo "Response: ${response%???}"
            exit 1
          fi
