name: 🚀 Deploy to Production

on:
  workflow_dispatch:
    inputs:
      build_number:
        description: 'Build number to deploy (leave empty for latest)'
        required: false
        type: string
      commit_sha:
        description: 'Commit SHA to deploy (leave empty for latest)'
        required: false
        type: string
      confirmation:
        description: 'Type "DEPLOY" to confirm production deployment'
        required: true
        type: string
      reason:
        description: 'Reason for production deployment'
        required: true
        type: string

jobs:
  validate-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Validate confirmation
        run: |
          if [[ "${{ github.event.inputs.confirmation }}" != "DEPLOY" ]]; then
            echo "❌ Invalid confirmation. You must type 'DEPLOY' to proceed."
            exit 1
          fi
          echo "✅ Confirmation validated"

      - name: Set deployment variables
        id: vars
        run: |
          echo "repo_owner_lower=${GITHUB_REPOSITORY_OWNER,,}" >> $GITHUB_ENV
          
          # Use provided values or defaults
          BUILD_NUMBER="${{ github.event.inputs.build_number }}"
          COMMIT_SHA="${{ github.event.inputs.commit_sha }}"
          
          if [[ -z "$BUILD_NUMBER" ]]; then
            echo "IMAGE_TAG=latest" >> $GITHUB_ENV
            echo "BUILD_DISPLAY=latest" >> $GITHUB_ENV
          else
            echo "IMAGE_TAG=build-$BUILD_NUMBER" >> $GITHUB_ENV
            echo "BUILD_DISPLAY=Build #$BUILD_NUMBER" >> $GITHUB_ENV
          fi
          
          if [[ -z "$COMMIT_SHA" ]]; then
            echo "COMMIT_DISPLAY=latest" >> $GITHUB_ENV
          else
            echo "COMMIT_SHA=$COMMIT_SHA" >> $GITHUB_ENV
            echo "COMMIT_DISPLAY=${COMMIT_SHA:0:7}" >> $GITHUB_ENV
          fi

      - name: Verify images exist
        run: |
          echo "🔍 Verifying that images exist for deployment..."
          
          # Check if API image exists
          if docker manifest inspect ghcr.io/${{ env.repo_owner_lower }}/yezhome-api:${{ env.IMAGE_TAG }} > /dev/null 2>&1; then
            echo "✅ API image found: ghcr.io/${{ env.repo_owner_lower }}/yezhome-api:${{ env.IMAGE_TAG }}"
          else
            echo "❌ API image not found: ghcr.io/${{ env.repo_owner_lower }}/yezhome-api:${{ env.IMAGE_TAG }}"
            echo "Available tags:"
            curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
              "https://api.github.com/orgs/${{ env.repo_owner_lower }}/packages/container/yezhome-api/versions" | \
              jq -r '.[0:5] | .[] | .metadata.container.tags[]?' || echo "Could not fetch tags"
            exit 1
          fi
          
          # Check if Internal API image exists
          if docker manifest inspect ghcr.io/${{ env.repo_owner_lower }}/yezhome-internal-api:${{ env.IMAGE_TAG }} > /dev/null 2>&1; then
            echo "✅ Internal API image found: ghcr.io/${{ env.repo_owner_lower }}/yezhome-internal-api:${{ env.IMAGE_TAG }}"
          else
            echo "❌ Internal API image not found: ghcr.io/${{ env.repo_owner_lower }}/yezhome-internal-api:${{ env.IMAGE_TAG }}"
            exit 1
          fi

      - name: Deploy to Production
        run: |
          echo "🚀 Deploying to PRODUCTION environment..."
          echo "📦 Images: ${{ env.IMAGE_TAG }}"
          echo "🏗️  Build: ${{ env.BUILD_DISPLAY }}"
          echo "📝 Commit: ${{ env.COMMIT_DISPLAY }}"
          echo "💭 Reason: ${{ github.event.inputs.reason }}"
          echo "👤 Deployed by: ${{ github.actor }}"
          
          response=$(curl -s -w "%{http_code}" -X POST \
            -H "Accept: application/vnd.github+json" \
            -H "Authorization: token ${{ secrets.DEPLOY_PAT }}" \
            https://api.github.com/repos/${{ env.repo_owner_lower }}/YEZHome_Deploy/dispatches \
            -d '{
              "event_type": "deploy-production",
              "client_payload": {
                "source": "YEZHome",
                "environment": "production",
                "deploy_environment": "production",
                "image_tag": "${{ env.IMAGE_TAG }}",
                "build_number": "${{ github.event.inputs.build_number }}",
                "commit_sha": "${{ env.COMMIT_SHA }}",
                "reason": "${{ github.event.inputs.reason }}",
                "deployed_by": "${{ github.actor }}",
                "manual_deploy": true,
                "workflow_run_id": "${{ github.run_id }}"
              }
            }')
          
          http_code="${response: -3}"
          if [ "$http_code" -eq 204 ]; then
            echo "✅ Production deployment triggered successfully!"
            echo "🎯 Monitor deployment progress in YEZHome_Deploy repository"
          else
            echo "❌ Failed to trigger production deployment. HTTP code: $http_code"
            echo "Response: ${response%???}"
            exit 1
          fi

      - name: Create deployment summary
        run: |
          echo "## 🚀 Production Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Field | Value |" >> $GITHUB_STEP_SUMMARY
          echo "|-------|-------|" >> $GITHUB_STEP_SUMMARY
          echo "| **Environment** | Production |" >> $GITHUB_STEP_SUMMARY
          echo "| **Images** | \`${{ env.IMAGE_TAG }}\` |" >> $GITHUB_STEP_SUMMARY
          echo "| **Build** | ${{ env.BUILD_DISPLAY }} |" >> $GITHUB_STEP_SUMMARY
          echo "| **Commit** | ${{ env.COMMIT_DISPLAY }} |" >> $GITHUB_STEP_SUMMARY
          echo "| **Deployed by** | ${{ github.actor }} |" >> $GITHUB_STEP_SUMMARY
          echo "| **Reason** | ${{ github.event.inputs.reason }} |" >> $GITHUB_STEP_SUMMARY
          echo "| **Timestamp** | $(date -u '+%Y-%m-%d %H:%M:%S UTC') |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 🔗 Links" >> $GITHUB_STEP_SUMMARY
          echo "- [Monitor deployment progress](https://github.com/${{ env.repo_owner_lower }}/YEZHome_Deploy/actions)" >> $GITHUB_STEP_SUMMARY
          echo "- [Production API](http://your-droplet-ip:5000)" >> $GITHUB_STEP_SUMMARY
          echo "- [Production Frontend](http://your-droplet-ip:3000)" >> $GITHUB_STEP_SUMMARY
