using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using Shared.Constants;
using Shared.Responses;

public class ExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ExceptionMiddleware> _logger;
    private readonly IHostEnvironment _env;

    public ExceptionMiddleware(RequestDelegate next, ILogger<ExceptionMiddleware> logger, IHostEnvironment env)
    {
        _next = next;
        _logger = logger;
        _env = env;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);

            //// Check for 403 Forbidden AFTER the next middleware has run
            //if (context.Response.StatusCode == 403)
            //{
            //    await HandleAuthorizationFailureAsync(context);
            //}
        }
        catch (SecurityTokenExpiredException ex)
        {
            await HandleTokenExpiredAsync(context, ex);
        }
        catch (Exception ex)
        {
            await HandleExceptionAsync(context, ex);
        }
    }

    private async Task HandleTokenExpiredAsync(HttpContext context, SecurityTokenExpiredException ex)
    {
        _logger.LogError(ex,
            "{Message} {RequestMethod} {RequestPath}",
            ResponseMessages.TokenExpiredError,
            context.Request.Method,
            context.Request.Path);

        var apiResponse = ApiResponse<object>.Failure(ResponseMessages.TokenExpiredError);

        await WriteApiResponseAsync(context, apiResponse, StatusCodes.Status401Unauthorized);
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception ex)
    {
        _logger.LogError(ex,
            "{Message} {RequestMethod} {RequestPath}",
            ResponseMessages.InternalServerError,
            context.Request.Method,
            context.Request.Path);

        var errorMessage = ResponseMessages.InternalServerError;

        // In development, include stack trace in the message
        if (_env.IsDevelopment())
        {
            errorMessage = $"{ResponseMessages.InternalServerError}: {ex.Message}";
        }

        var apiResponse = ApiResponse<object>.Failure(errorMessage);

        await WriteApiResponseAsync(context, apiResponse, StatusCodes.Status500InternalServerError);
    }

    private async Task WriteApiResponseAsync(HttpContext context, ApiResponse<object> apiResponse, int statusCode)
    {
        // If response has already started, we can't modify it to avoid errors
        if (context.Response.HasStarted)
        {
            _logger.LogWarning(
            "{Message} {RequestMethod} {RequestPath}",
            ResponseMessages.ResponseAlreadyStarted,
            context.Request.Method,
            context.Request.Path);
            return;
        }

        context.Response.ContentType = "application/json";
        context.Response.StatusCode = statusCode;
        await context.Response.WriteAsJsonAsync(apiResponse);
    }
}