using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using Shared.Constants;

public class ExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ExceptionMiddleware> _logger;
    private readonly IHostEnvironment _env;

    public ExceptionMiddleware(RequestDelegate next, ILogger<ExceptionMiddleware> logger, IHostEnvironment env)
    {
        _next = next;
        _logger = logger;
        _env = env;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);

            //// Check for 403 Forbidden AFTER the next middleware has run
            //if (context.Response.StatusCode == 403)
            //{
            //    await HandleAuthorizationFailureAsync(context);
            //}
        }
        catch (SecurityTokenExpiredException ex)
        {
            await HandleTokenExpiredAsync(context, ex);
        }
        catch (Exception ex)
        {
            await HandleExceptionAsync(context, ex);
        }
    }

    private async Task HandleTokenExpiredAsync(HttpContext context, SecurityTokenExpiredException ex)
    {
        _logger.LogError(ex,
            "{Message} {RequestMethod} {RequestPath}",
            ResponseMessages.TokenExpiredError,
            context.Request.Method,
            context.Request.Path);

        var problemDetails = new ProblemDetails
        {
            Status = StatusCodes.Status401Unauthorized,
            Title = "token_expired",
            Detail = ResponseMessages.TokenExpiredError
        };

        await WriteProblemDetailsAsync(context, problemDetails);
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception ex)
    {
        _logger.LogError(ex,
            "{Message} {RequestMethod} {RequestPath}",
            ResponseMessages.InternalServerError,
            context.Request.Method,
            context.Request.Path);

        var problemDetails = new ProblemDetails
        {
            Status = StatusCodes.Status500InternalServerError,
            Title = ResponseMessages.InternalServerError
        };

        if (_env.IsDevelopment())
        {
            problemDetails.Detail = ex.StackTrace;
        }

        await WriteProblemDetailsAsync(context, problemDetails);
    }

    private async Task WriteProblemDetailsAsync(HttpContext context, ProblemDetails problemDetails)
    {
        // Nếu response đã bắt đầu được gửi đi, không làm gì cả để tránh lỗi
        if (context.Response.HasStarted)
        {
            _logger.LogWarning(
            "{Message} {RequestMethod} {RequestPath}",
            ResponseMessages.ResponseAlreadyStarted,
            context.Request.Method,
            context.Request.Path);
            return;
        }

        context.Response.ContentType = "application/problem+json";
        context.Response.StatusCode = problemDetails.Status ?? StatusCodes.Status500InternalServerError;
        await context.Response.WriteAsJsonAsync(problemDetails);

    }
}