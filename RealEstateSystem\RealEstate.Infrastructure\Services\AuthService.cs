﻿using Amazon.Runtime.Internal;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Common;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using Shared.Constants;
using Shared.Enums;
using Shared.Helper;
using Shared.Results;
using System.ComponentModel.DataAnnotations;
using System.Security.Cryptography;
using System.Text;

namespace RealEstate.Application.Services
{
    public class AuthService : IAuthService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ITokenService _tokenService;
        private readonly IMapper _mapper;

        public AuthService(IUnitOfWork unitOfWork, ITokenService tokenService, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _tokenService = tokenService;
            _mapper = mapper;
        }

        public async Task<Result<UserDto>> RegisterAsync(CreateUserDto registerDto)
        {
            var normalizedPhone = PhoneUtils.NormalizePhoneNumber(registerDto.Phone);
            if (normalizedPhone == null)
                return Result<UserDto>.Failure($"Số điện thoại không hợp lệ", ErrorType.Validation);

            if (await _unitOfWork.AppUsers.EmailExistsAsync(registerDto.GetNormalizedEmail()))
            {
                return Result<UserDto>.Failure($"Địa chỉ email {registerDto.GetNormalizedEmail()} đã được sử dụng", ErrorType.Conflict);
            }

            if (await _unitOfWork.AppUsers.PhoneExistsAsync(normalizedPhone))
            {
                return Result<UserDto>.Failure($"Số điện thoại {normalizedPhone} đã được sử dụng", ErrorType.Conflict);
            }

            // Generate salt and hash
            var salt = GenerateRandomSalt();
            var hashedPassword = HashPassword(registerDto.Password, salt);

            var user = new AppUser
            {
                Email = registerDto.GetNormalizedEmail(),
                FullName = registerDto.FullName,
                PasswordHash = hashedPassword,
                PasswordSalt = salt,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = Guid.Empty,
                Phone = normalizedPhone,
                UserType = registerDto.UserType,
            };

            await _unitOfWork.AppUsers.AddAsync(user);

            // Create default notification preferences for the user
            var notificationPreferences = new NotificationPreference
            {
                UserId = user.Id,
                ReceivePromotions = true,
                ReceiveWalletUpdates = true,
                ReceiveNews = true,
                ReceiveCustomerMessages = true,
                CreatedAt = DateTime.UtcNow
            };

            await _unitOfWork.NotificationPreferences.AddAsync(notificationPreferences);

            // Create default wallet for the user
            var wallet = new Wallet
            {
                UserId = user.Id,
                Balance = 0
            };

            await _unitOfWork.Wallets.AddAsync(wallet);

            await _unitOfWork.SaveChangesAsync();

            var accessToken = _tokenService.CreateToken(user);

            var userDto = new UserDto
            {
                Id = user.Id,
                Email = user.Email,
                FullName = user.FullName,
                Phone = user.Phone,
                LastLogin = null,
                Token = accessToken,
                IsActive = user.IsActive,
                UserType = user.UserType,
            };

            return Result<UserDto>.Success(userDto);
        }

        public async Task<Result<UserDto>> LoginAsync(LoginDto loginDto)
        {
            var user = await _unitOfWork.AppUsers.GetByEmailAsync(loginDto.GetNormalizedEmail(), asNoTracking: false, isIncludeRole: true);
            if (user == null)
            {
                return Result<UserDto>.Failure(AuthenticateMessage.UsernamePasswordNotValid, ErrorType.Validation);
            }

            var hashedPassword = HashPassword(loginDto.Password, user.PasswordSalt);
            if (hashedPassword != user.PasswordHash)
            {
                return Result<UserDto>.Failure(AuthenticateMessage.UsernamePasswordNotValid, ErrorType.Validation);
            }

            // Check if the user account is active
            if (!user.IsActive)
            {
                return Result<UserDto>.Failure(AuthenticateMessage.AccountUnabled, ErrorType.Validation);
            }

            var accessToken = _tokenService.CreateToken(user);

            // Update last login time with explicit UTC kind specification
            try
            {
                user.LastLogin = DateTime.SpecifyKind(DateTime.UtcNow, DateTimeKind.Utc);
                _unitOfWork.AppUsers.Update(user);
                await _unitOfWork.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                // Log the error but don't fail the login process
                // The user can still login successfully even if LastLogin update fails
                Console.WriteLine($"Warning: Failed to update LastLogin for user {user.Email}: {ex.Message}");
            }

            var userDto = new UserDto
            {
                Id = user.Id,
                Email = user.Email,
                FullName = user.FullName,
                Phone = user.Phone,
                LastLogin = user.LastLogin,
                Token = accessToken,
                IsActive = user.IsActive,
                UserType = user.UserType,
            };

            // Include role and permission data for admin users
            if (user.UserType == EnumValues.UserType.Admin)
            {
                // Get user role codes
                var roleCodes = user.UserRoles?.Select(ur => ur.Role.Code).ToList() ?? new List<string>();
                userDto.Roles = roleCodes;

                // Get user permission codes from roles
                var permissionCodes = user.UserRoles?
                    .SelectMany(ur => ur.Role.RolePermissions ?? new List<RolePermission>())
                    .Select(rp => rp.Permission.Code)
                    .Distinct()
                    .ToList() ?? new List<string>();
                userDto.Permissions = permissionCodes;

                // Get role objects (new detailed objects)
                var roles = user.UserRoles?.Select(ur => ur.Role).ToList() ?? new List<AdminRole>();
                userDto.RoleObjects = _mapper.Map<IEnumerable<AdminRoleDto>>(roles);

                // Get permission objects (new detailed objects)
                var permissions = user.UserRoles?
                    .SelectMany(ur => ur.Role.RolePermissions ?? new List<RolePermission>())
                    .Select(rp => rp.Permission)
                    .Distinct()
                    .ToList() ?? new List<Permission>();
                userDto.PermissionObjects = _mapper.Map<IEnumerable<PermissionDto>>(permissions);
            }

            return Result<UserDto>.Success(userDto);
        }

        public async Task<Result> ChangePassword(ChangePasswordDto changePasswordDto)
        {
            var user = await _unitOfWork.AppUsers.GetByEmailAsync(changePasswordDto.GetNormalizedEmail(), asNoTracking: false);
            if (user == null)
            {
                return Result<UserDto>.Failure(AuthenticateMessage.UsernamePasswordNotValid, ErrorType.Validation);
            }

            var hashedOldPassword = HashPassword(changePasswordDto.OldPassword, user.PasswordSalt);
            if (hashedOldPassword != user.PasswordHash)
            {
                return Result<UserDto>.Failure(AuthenticateMessage.OldPasswordNotCorrect, ErrorType.Validation);
            }

            // Generate new salt and hash the new password
            var newSalt = GenerateRandomSalt();
            var newHashedPassword = HashPassword(changePasswordDto.NewPassword, newSalt);

            user.PasswordSalt = newSalt;
            user.PasswordHash = newHashedPassword;

            _unitOfWork.AppUsers.Update(user);
            await _unitOfWork.SaveChangesAsync();

            return Result.Success();
        }

        public async Task<Result<UserDto>> RefreshToken(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null)
                throw new UnauthorizedAccessException("User not found");
            var userDto = new UserDto
            {
                Id = user.Id,
                Email = user.Email,
                FullName = user.FullName,
                Phone = user.Phone,
                LastLogin = user.LastLogin,
                Token = _tokenService.CreateToken(user),
            };
            return Result<UserDto>.Success(userDto);
        }

        private static string GenerateRandomSalt()
        {
            byte[] salt = new byte[128 / 8];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(salt);
            }
            return Convert.ToBase64String(salt);
        }

        private static string HashPassword(string password, string salt)
        {
            using (var sha256 = SHA256.Create())
            {
                var saltedPassword = string.Concat(password, salt);
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        public async Task<bool> ValidateUserCredentialsAsync(string email, string password)
        {
            var user = await _unitOfWork.AppUsers.GetByEmailAsync(email);
            if (user == null)
                return false;

            var hashedPassword = HashPassword(password, user.PasswordSalt);
            return hashedPassword == user.PasswordHash;
        }
    }
}
