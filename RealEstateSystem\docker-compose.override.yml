services:
  # Development overrides for RealEstate.API
  yezhome-api:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      # Development database connection
      - ConnectionStrings__YezHomeConnection=Host=yezhome_postgres;Database=yezhome_dev_db;Username=postgres;Password=dev_password
      # Development JWT settings (use a simple key for development)
      - JWT__Key=development_jwt_secret_key_32_chars_min
      - JWT__Issuer=yezhomeAPI-Dev
      - JWT__Audience=yezhomeUsers-Dev
      # Development CORS - allow all origins
      - Cors__AllowedOrigins__0=http://localhost:3000
      - Cors__AllowedOrigins__1=http://localhost:3001
      - Cors__AllowedOrigins__2=http://127.0.0.1:3000
      - Cors__AllowedOrigins__3=http://127.0.0.1:3001
      # Use local storage for development
      - Storage__Provider=Local
      # Development email settings (use fake/test values)
      - InternalEmail__NoReply=noreply@localhost
      - InternalEmail__Support=support@localhost
      - InternalEmail__ReivewPost=review@localhost
    volumes:
      # Mount local directories for development
      - ./RealEstate.API/PropertyImages:/app/PropertyImages
      - ./RealEstate.API/UserAvatars:/app/UserAvatars
      - ./RealEstate.API/Temp:/app/Temp
      - ./RealEstate.API/Templates:/app/Templates

  # Development overrides for RealEstate.InternalAPI
  yezhome-internal-api:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      # Development database connection
      - ConnectionStrings__YezHomeConnection=Host=yezhome_postgres;Database=yezhome_dev_db;Username=postgres;Password=dev_password
      # Development JWT settings
      - JWT__Key=development_jwt_secret_key_32_chars_min
      - JWT__Issuer=yezhomeInternalAPI-Dev
      - JWT__Audience=yezhomeAdmins-Dev
      # Development CORS - allow all origins
      - Cors__AllowedOrigins__0=http://localhost:3000
      - Cors__AllowedOrigins__1=http://localhost:3001
      - Cors__AllowedOrigins__2=http://127.0.0.1:3000
      - Cors__AllowedOrigins__3=http://127.0.0.1:3001
